import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddFlowerAlarmWidget extends ConsumerWidget {
  const AddFlowerAlarmWidget({
    super.key,
    required this.type,
    required this.pickerData,
    required this.flower,
  });

  final NurtureType type;
  final List<String> pickerData;
  final Flower? flower;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cycle = ref.watch(
      addFlowerControllerProvider(flower).select((asyncState) =>
        asyncState.when(
          data: (state) => state.getNurtureTypeCycle(type),
          loading: () => 0,
          error: (_, __) => 0,
        )
      )
    );

    Widget cycleText;
    if (cycle == monthlyCycleDataUnset) {
      cycleText = Text('monthly_cycle_settings.unset'.tr(), style: const TextStyle(color: Colors.black54));
    } else if (cycle == monthlyCycleDataInherit) {
      cycleText = Text('monthly_cycle_settings.inherit'.tr(), style: const TextStyle(color: Colors.black54));
    } else {
      cycleText = Text('$cycle${'monthly_cycle_settings.days_unit'.tr()}');
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      child: buildBody(cycleText),
      onTap: () => _showCyclePicker(context, ref),
    );
  }

  Widget buildBody(Widget cycle) {
    return Container(
        padding: const EdgeInsets.all(5),
        child: Row(children: [
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SvgPicture.asset(type.icon, width: 16, height: 16),
          ),
          Text(type.name),
          const Spacer(),
          cycle,
          const SizedBox(width: 5),
          const Icon(Icons.edit)
        ])
    );
  }

  void _showCyclePicker(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CyclePickerBottomSheet(
        type: type,
        flower: flower,
        onCycleSelected: (cycle, applyToAllMonths) {
          if (applyToAllMonths) {
            ref.read(addFlowerControllerProvider(flower).notifier)
                .setNurtureTypeCycleForAllMonths(type, cycle);
          } else {
            final currentMonth = DateTime.now().month;
            ref.read(addFlowerControllerProvider(flower).notifier)
                .setNurtureTypeCycleForMonth(type, currentMonth, cycle);
          }
        },
      ),
    );
  }
}

class _CyclePickerBottomSheet extends StatefulWidget {
  const _CyclePickerBottomSheet({
    required this.type,
    required this.flower,
    required this.onCycleSelected,
  });

  final NurtureType type;
  final Flower? flower;
  final Function(int cycle, bool applyToAllMonths) onCycleSelected;

  @override
  State<_CyclePickerBottomSheet> createState() => _CyclePickerBottomSheetState();
}

class _CyclePickerBottomSheetState extends State<_CyclePickerBottomSheet> {
  int _selectedCycle = monthlyCycleDataInherit;
  final TextEditingController _daysController = TextEditingController();
  String? _errorText;

  @override
  void dispose() {
    _daysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          top: 20,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'monthly_cycle_settings.cycle_picker_title'.tr(),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildOptionTile(
              title: 'monthly_cycle_settings.inherit'.tr(),
              subtitle: 'monthly_cycle_settings.inherit_description'.tr(),
              value: monthlyCycleDataInherit,
            ),
            _buildOptionTile(
              title: 'monthly_cycle_settings.unset'.tr(),
              subtitle: 'monthly_cycle_settings.unset_description'.tr(),
              value: monthlyCycleDataUnset,
            ),
            _buildCustomDaysOption(),
            if (_errorText != null) ...[
              const SizedBox(height: 8),
              Text(
                _errorText!,
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
            ],
            const SizedBox(height: 30),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _onConfirm(false),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.black87,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text('monthly_cycle_settings.apply_current_month'.tr()),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _onConfirm(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text('monthly_cycle_settings.apply_all_months'.tr()),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required String title,
    required String subtitle,
    required int value,
  }) {
    return RadioListTile<int>(
      title: Text(title),
      subtitle: Text(
        subtitle,
        style: TextStyle(color: Colors.grey[600], fontSize: 12),
      ),
      value: value,
      groupValue: _selectedCycle,
      onChanged: (int? newValue) {
        if (newValue != null) {
          setState(() {
            _selectedCycle = newValue;
            _errorText = null;
          });
        }
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildCustomDaysOption() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RadioListTile<int>(
          title: Text('monthly_cycle_settings.custom_days'.tr()),
          value: -2, // 使用特殊值表示自定义天数
          groupValue: _selectedCycle > 0 ? -2 : _selectedCycle,
          onChanged: (int? newValue) {
            setState(() {
              _selectedCycle = 1; // 默认设置为1天
              _daysController.text = '1';
              _errorText = null;
            });
          },
          contentPadding: EdgeInsets.zero,
        ),
        if (_selectedCycle > 0)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
            child: TextField(
              controller: _daysController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                hintText: 'monthly_cycle_settings.input_days_hint'.tr(),
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                errorText: _errorText,
              ),
              onChanged: (value) {
                final days = int.tryParse(value);
                if (days != null && days >= 1 && days <= 366) {
                  setState(() {
                    _selectedCycle = days;
                    _errorText = null;
                  });
                } else if (value.isNotEmpty) {
                  setState(() {
                    _errorText = 'monthly_cycle_settings.invalid_days_error'.tr();
                  });
                }
              },
            ),
          ),
      ],
    );
  }

  void _onConfirm(bool applyToAllMonths) {
    if (_selectedCycle > 0) {
      final days = int.tryParse(_daysController.text);
      if (days == null || days < 1 || days > 366) {
        setState(() {
          _errorText = 'monthly_cycle_settings.invalid_days_error'.tr();
        });
        return;
      }
      _selectedCycle = days;
    }

    widget.onCycleSelected(_selectedCycle, applyToAllMonths);
    Navigator.pop(context);
  }
}