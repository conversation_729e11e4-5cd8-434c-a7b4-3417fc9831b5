import 'dart:io';
import 'dart:typed_data';

import 'package:flower_timemachine/common/file_utils.dart';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_flower_controller.g.dart';
part 'add_flower_controller.freezed.dart';

@freezed
class AddFlowerState with _$AddFlowerState {
  const factory AddFlowerState({
    required Flower? flower,
    required FlowerMonthlyCycles monthlyCycles,
    Uint8List? newAvatarData,
    String? newName,
    @Default(false) bool isChange,
    List<TagInfo>? newSelectedTags,
    DateTime? newArrivalTime,
  }) = _AddFlowerState;

  const AddFlowerState._();

  Image get avatar {
    final avatar = flower?.avatar;
    if (avatar != null) {
      return Image.file(File(avatar));
    } else {
      return Image.asset(R.iconFlower);
    }
  }

  List<TagInfo> get selectedTags => List.unmodifiable(flower?.tags ?? []);

  String? get name {
    return newName ?? flower?.name;
  }

  DateTime get arrivalTime {
    if (newArrivalTime != null) {
      return newArrivalTime!;
    }

    if (flower?.arrivalTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(flower!.arrivalTime! * 1000);
    }
    if (flower?.createTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(flower!.createTime * 1000);
    }

    return DateTime.now();
  }

  int getNurtureTypeCycle(NurtureType type) {
    final now = DateTime.now();
    return monthlyCycles.getEffectiveCycleForTypeAndMonth(type, now.month);
  }
}

@riverpod
class AddFlowerController extends _$AddFlowerController {
  @override
  Future<AddFlowerState> build(Flower? flower) async {
    FlowerMonthlyCycles monthlyCycles;

    if (flower == null) {
      monthlyCycles = FlowerMonthlyCycles();
    } else {
      monthlyCycles = await FlowerMonthlyNurtureCycle.getFlowerMonthlyCycles(flower.id);
    }

    return AddFlowerState(
      flower: flower,
      monthlyCycles: monthlyCycles,
    );
  }

  void setNurtureTypeCycle(NurtureType type, int cycle) {
    final currentState = state.value;
    if (currentState == null) return;

    final newMonthlyCycles = currentState.monthlyCycles.copy();
    newMonthlyCycles.setCycleForTypeAndMonth(type, 1, cycle);

    state = AsyncValue.data(currentState.copyWith(
      monthlyCycles: newMonthlyCycles,
      isChange: true,
    ));
  }

  void setNurtureTypeCycleForMonth(NurtureType type, int month, int cycle) {
    final currentState = state.value;
    if (currentState == null) return;

    final newMonthlyCycles = currentState.monthlyCycles.copy();
    newMonthlyCycles.setCycleForTypeAndMonth(type, month, cycle);

    state = AsyncValue.data(currentState.copyWith(
      monthlyCycles: newMonthlyCycles,
      isChange: true,
    ));
  }

  void setNurtureTypeCycleForAllMonths(NurtureType type, int cycle) {
    final currentState = state.value;
    if (currentState == null) return;

    final newMonthlyCycles = currentState.monthlyCycles.copy();
    for (int month = 1; month <= 12; month++) {
      newMonthlyCycles.setCycleForTypeAndMonth(type, month, cycle);
    }

    state = AsyncValue.data(currentState.copyWith(
      monthlyCycles: newMonthlyCycles,
      isChange: true,
    ));
  }

  void setNewAvatar(Uint8List image) {
    final currentState = state.value;
    if (currentState == null) return;

    state = AsyncValue.data(currentState.copyWith(
      newAvatarData: image,
      isChange: true,
    ));
  }

  void updateMonthlyCycles(FlowerMonthlyCycles newCycles) {
    final currentState = state.value;
    if (currentState == null) return;

    state = AsyncValue.data(currentState.copyWith(
      monthlyCycles: newCycles,
      isChange: true,
    ));
  }

  void setSelectedTags(List<TagInfo> newTags) {
    final currentState = state.value;
    if (currentState == null) return;

    final currentTags = currentState.flower?.tags ?? [];
    if (const ListEquality().equals(currentTags, newTags)) {
      return;
    }

    final newSelectedTags = <TagInfo>[];
    newSelectedTags.addAll(newTags);

    state = AsyncValue.data(currentState.copyWith(
      newSelectedTags: newSelectedTags,
      isChange: true,
    ));
  }

  void setName(String? v) {
    final currentState = state.value;
    if (currentState == null) return;

    if (currentState.flower == null && (v == null || v == "")) {
      return;
    }
    if (v == currentState.flower?.name) {
      return;
    }

    state = AsyncValue.data(currentState.copyWith(
      newName: v,
      isChange: true,
    ));
  }

  void setArrivalTime(DateTime time) {
    final currentState = state.value;
    if (currentState == null) return;

    if (currentState.newArrivalTime == time) {
      return;
    }

    state = AsyncValue.data(currentState.copyWith(
      newArrivalTime: time,
      isChange: true,
    ));
  }

  Future<Flower> save() async {
    final currentState = state.value;
    if (currentState == null) throw Exception('State not loaded');

    String? avatarFile;
    if (currentState.newAvatarData != null) {
      final littleData = await FlutterImageCompress.compressWithList(
        currentState.newAvatarData!,
        minHeight: 256,
        minWidth: 256
      );
      avatarFile = await writeToAvatarRecordDir(littleData);
    }

    if (currentState.flower == null) {
      return await _createFlower(currentState, avatarFile);
    } else {
      await _updateFlower(currentState, avatarFile);
      return currentState.flower!;
    }
  }

  Future<Flower> _createFlower(AddFlowerState currentState, String? avatarFile) async {
    int? arrivalTime;
    if (currentState.newArrivalTime != null) {
      arrivalTime = (currentState.newArrivalTime!.millisecondsSinceEpoch / 1000).truncate();
    }

    return Flower.create(
        name: currentState.newName!,
        avatar: avatarFile,
        tags: currentState.newSelectedTags,
        monthlyCycles: currentState.monthlyCycles,
        arrivalTime: arrivalTime);
  }

  Future<void> _updateFlower(AddFlowerState currentState, String? avatarFile) async {
    int? arrivalTime;
    if (currentState.newArrivalTime != null) {
      arrivalTime = (currentState.newArrivalTime!.millisecondsSinceEpoch / 1000).truncate();
    }

    currentState.flower!.update(
        name: currentState.newName,
        avatar: avatarFile,
        tags: currentState.newSelectedTags,
        monthlyCycles: currentState.monthlyCycles,
        arrivalTime: arrivalTime);
  }
}
